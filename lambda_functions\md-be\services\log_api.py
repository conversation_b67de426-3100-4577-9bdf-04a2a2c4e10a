from sqlalchemy.orm import Session
from sqlalchemy import and_, func, text
from models.log import CoLog
from schemas.log import LogFilters, LogExportRequest, LogResponse
from typing import List, Dict, Any, Optional, Tuple
import csv
import io
from datetime import datetime
from uuid import UUID

def build_log_query(db: Session, filters: LogFilters):
    """
    Build a query that applies filters to the log table
    """
    # Extract JSON fields from context as separate columns
    base_query = db.query(
        CoLog.id,
        CoLog.uuid,
        CoLog.timestamp,
        CoLog.userUuid,
        CoLog.action,
        CoLog.context['filters_applied'].label('filters_applied'),
        CoLog.context['selected_fields'].label('selected_fields'),
        CoLog.purpose
    )
    
    filter_conditions = []
    
    # Time range filters
    if filters.start_timestamp:
        filter_conditions.append(CoLog.timestamp >= filters.start_timestamp)
    
    if filters.end_timestamp:
        filter_conditions.append(CoLog.timestamp <= filters.end_timestamp)
    
    # Purpose text search (case-insensitive)
    if filters.purpose:
        filter_conditions.append(CoLog.purpose.ilike(f"%{filters.purpose}%"))
    
    # Action filter (exact match)
    if filters.action:
        filter_conditions.append(CoLog.action == filters.action)
    
    # Apply all filters
    if filter_conditions:
        base_query = base_query.filter(and_(*filter_conditions))
    
    return base_query

def get_logs_with_pagination(
    db: Session, 
    filters: LogFilters, 
    page: int = 1, 
    page_size: int = 50
) -> Tuple[List[LogResponse], int]:
    """
    Get logs with pagination and filtering
    """
    # Build base query
    query = build_log_query(db, filters)
    
    # Get total count
    total_count = query.count()
    
    # Apply pagination
    offset = (page - 1) * page_size
    logs = query.order_by(CoLog.timestamp.desc()).offset(offset).limit(page_size).all()
    
    # Convert to response models
    log_responses = []
    for log in logs:
        log_response = LogResponse(
            id=log.id,
            uuid=log.uuid,
            timestamp=log.timestamp,
            userUuid=log.userUuid,
            action=log.action,
            filters_applied=log.filters_applied,
            selected_fields=log.selected_fields,
            purpose=log.purpose
        )
        log_responses.append(log_response)
    
    return log_responses, total_count

def format_field_value_for_csv(value: Any) -> str:
    """
    Format a field value for CSV output
    """
    if value is None:
        return ''
    elif isinstance(value, datetime):
        return value.strftime('%Y-%m-%d %H:%M:%S')
    elif isinstance(value, UUID):
        return str(value)
    elif isinstance(value, (dict, list)):
        # Convert JSON data to compact string representation
        import json
        try:
            # Use compact JSON format without extra spaces
            json_str = json.dumps(value, separators=(',', ':'), default=str)
            # Remove newlines and extra spaces for CSV compatibility
            json_str = json_str.replace('\n', ' ').replace('\r', ' ').strip()
            return json_str
        except:
            return str(value)
    else:
        return str(value)

def get_field_value_from_log(log_data: Any, field: str) -> Any:
    """
    Get the value for a field from log data
    """
    if field == 'timestamp':
        return log_data.timestamp
    elif field == 'userUuid':
        return log_data.userUuid
    elif field == 'action':
        return log_data.action
    elif field == 'filters_applied':
        return log_data.filters_applied
    elif field == 'selected_fields':
        return log_data.selected_fields
    elif field == 'purpose':
        return log_data.purpose
    else:
        return None

def generate_csv_from_logs(logs: List[Any], selected_fields: List[str]) -> str:
    """
    Generate CSV content from logs with selected fields
    """
    if not logs:
        return ""
    
    # Create StringIO object for CSV content
    csv_output = io.StringIO()
    
    # Map database field names to display names for CSV headers
    field_mapping = {
        'timestamp': 'Timestamp',
        'userUuid': 'User UUID',
        'action': 'Action',
        'filters_applied': 'Filters Applied',
        'selected_fields': 'Selected Fields',
        'purpose': 'Purpose'
    }
    
    # Create CSV headers based on selected fields
    headers = [field_mapping.get(field, field) for field in selected_fields]
    
    writer = csv.writer(csv_output, quoting=csv.QUOTE_MINIMAL)
    writer.writerow(headers)
    
    # Write data rows
    for log in logs:
        row = []
        for field in selected_fields:
            value = get_field_value_from_log(log, field)
            row.append(format_field_value_for_csv(value))
        writer.writerow(row)
    
    csv_content = csv_output.getvalue()
    csv_output.close()
    
    return csv_content

def log_export_action(
    db: Session, 
    user_uuid: UUID, 
    request_data: LogExportRequest, 
    filters_applied: Dict[str, Any]
) -> None:
    """
    Log the export action to the universal log table
    """
    try:
        context_data = {
            "selected_fields": request_data.selected_fields,
            "filters_applied": filters_applied,
            "notes": request_data.notes
        }
        
        log_entry = CoLog(
            userUuid=user_uuid,
            action="log_export",
            context=context_data,
            purpose=request_data.notes or "Log export"
        )
        
        db.add(log_entry)
        db.commit()
        
    except Exception as e:
        # Log the error but don't fail the export
        print(f"Warning: Failed to log export action: {str(e)}")
        db.rollback()

def export_logs(
    db: Session,
    request_data: LogExportRequest,
    user_uuid: UUID
) -> str:
    """
    Main export function that handles the complete log export process
    """
    # Get all logs that match the filters (no pagination for export)
    query = build_log_query(db, request_data.filters)
    logs = query.order_by(CoLog.timestamp.desc()).all()
    
    if not logs:
        # No logs found - return empty CSV with headers
        csv_output = io.StringIO()
        writer = csv.writer(csv_output, quoting=csv.QUOTE_MINIMAL)
        
        # Create headers
        field_mapping = {
            'timestamp': 'Timestamp',
            'userUuid': 'User UUID',
            'action': 'Action',
            'filters_applied': 'Filters Applied',
            'selected_fields': 'Selected Fields',
            'purpose': 'Purpose'
        }
        headers = [field_mapping.get(field, field) for field in request_data.selected_fields]
        writer.writerow(headers)
        
        csv_content = csv_output.getvalue()
        csv_output.close()
        
        # Log the export action
        log_export_action(db, user_uuid, request_data, {})
        return csv_content
    
    # Generate CSV content
    csv_content = generate_csv_from_logs(logs, request_data.selected_fields)
    
    # Prepare filters for logging (only include non-None values)
    filters_applied = {}
    if request_data.filters:
        for field, value in request_data.filters.model_dump().items():
            if value is not None:
                if isinstance(value, datetime):
                    filters_applied[field] = value.isoformat()
                else:
                    filters_applied[field] = value
    
    # Log the export action
    log_export_action(db, user_uuid, request_data, filters_applied)
    
    return csv_content

def get_available_actions(db: Session) -> List[str]:
    """
    Get list of unique actions available in the log table
    """
    actions = db.query(CoLog.action).distinct().all()
    return [action[0] for action in actions if action[0]] 