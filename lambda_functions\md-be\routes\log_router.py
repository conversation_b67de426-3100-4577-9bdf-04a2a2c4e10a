from fastapi import APIRouter, Depends, Query, HTTPException
from sqlalchemy.orm import Session
from db.db import get_db
from controller.log_controller import (
    list_logs,
    export_logs_to_csv,
    get_log_actions,
    get_log_export_fields
)
from schemas.log import LogFilters, LogExportRequest
from dependencies.admin_jwt import validate_jwt_token
from dependencies.member_jwt import validate_token as member_validate_jwt_token
from typing import Optional, Union
from datetime import datetime
from uuid import UUID

router = APIRouter(prefix="/logs", tags=["logs"])

@router.get("/")
async def get_logs(
    start_timestamp: Optional[datetime] = Query(None, description="Start timestamp for filtering"),
    end_timestamp: Optional[datetime] = Query(None, description="End timestamp for filtering"),
    purpose: Optional[str] = Query(None, description="Search text in purpose field"),
    action: Optional[str] = Query(None, description="Filter by action type"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of items per page"),
    db: Session = Depends(get_db),
    current_user = Depends(validate_jwt_token)  # Only admin users can view logs
):
    """
    Get logs with optional filtering and pagination
    
    **Filters:**
    - `start_timestamp`: Filter logs from this timestamp onwards
    - `end_timestamp`: Filter logs until this timestamp
    - `purpose`: Search for text within the purpose field (case-insensitive)
    - `action`: Filter by specific action type (exact match)
    
    **Pagination:**
    - `page`: Page number (starts from 1)
    - `page_size`: Number of items per page (1-1000)
    
    **Response includes:**
    - `logs`: List of log entries with extracted context fields
    - `total_count`: Total number of logs matching the filters
    - `page`: Current page number
    - `page_size`: Items per page
    """
    filters = LogFilters(
        start_timestamp=start_timestamp,
        end_timestamp=end_timestamp,
        purpose=purpose,
        action=action
    )
    
    return list_logs(filters, page, page_size, db)

@router.post("/export")
async def export_logs(
    request: LogExportRequest,
    db: Session = Depends(get_db),
    current_user = Depends(validate_jwt_token)  # Only admin users can export logs
):
    """
    Export logs to CSV based on filters and selected fields
    
    **Request Body:**
    - `filters`: Same filtering options as GET /logs endpoint
    - `selected_fields`: Array of field names to include in CSV
    - `notes`: Optional description of export purpose
    
    **Available Fields:**
    - `timestamp`: When the log entry was created
    - `userUuid`: UUID of the user who performed the action
    - `action`: Type of action performed
    - `filters_applied`: Filters applied from context JSON
    - `selected_fields`: Selected fields from context JSON
    - `purpose`: Purpose or description of the action
    
    **Response:**
    - `csv_content`: Base64 encoded CSV content
    - `filename`: Suggested filename for download
    - `export_timestamp`: When the export was generated
    
    **Note:** This action itself will be logged in the system
    """
    # Get user UUID from current user (admin or member)
    user_uuid = current_user.get("sub") 
    if not user_uuid:
        raise HTTPException(status_code=401, detail="User UUID not found in token")
    
    if isinstance(user_uuid, str):
        user_uuid = UUID(user_uuid)
    
    return export_logs_to_csv(request, user_uuid, db)

@router.get("/actions")
async def get_available_actions(
    db: Session = Depends(get_db),
    current_user = Depends(validate_jwt_token)  # Only admin users can view available actions
):
    """
    Get list of unique action types available in the log table
    
    **Response:**
    - `actions`: Array of unique action strings found in the database
    
    This endpoint is useful for populating filter dropdowns in the frontend.
    """
    return get_log_actions(db)

@router.get("/export-fields")
async def get_export_fields(
    current_user = Depends(validate_jwt_token)  # Only admin users can view export fields
):
    """
    Get available fields for CSV export
    
    **Response:**
    - `fields`: Array of field objects with:
      - `field`: Field name for API requests
      - `display_name`: Human-readable field name
      - `description`: Description of what the field contains
    
    This endpoint is useful for building dynamic export field selection interfaces.
    """
    return get_log_export_fields()

# Alternative endpoint that allows both admin and member access (if needed)
@router.get("/my-logs")
async def get_my_logs(
    start_timestamp: Optional[datetime] = Query(None, description="Start timestamp for filtering"),
    end_timestamp: Optional[datetime] = Query(None, description="End timestamp for filtering"),
    purpose: Optional[str] = Query(None, description="Search text in purpose field"),
    action: Optional[str] = Query(None, description="Filter by action type"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of items per page"),
    db: Session = Depends(get_db),
    current_user: Union[dict, None] = Depends(lambda: None)  # Try both auth methods
):
    """
    Get logs for the current user (members can only see their own logs)
    
    This endpoint automatically filters logs by the current user's UUID.
    Members can only see their own activity logs.
    Admins can see all logs through the main /logs endpoint.
    """
    # Try to get user from either admin or member auth
    try:
        if not current_user:
            try:
                current_user = validate_jwt_token()
            except:
                current_user = member_validate_jwt_token()
    except:
        raise HTTPException(status_code=401, detail="Authentication required")
    
    # Get user UUID from current user
    user_uuid = current_user.get("uuid") or current_user.get("userUuid")
    if not user_uuid:
        raise HTTPException(status_code=401, detail="User UUID not found in token")
    
    # Create filters with additional user UUID filter
    filters = LogFilters(
        start_timestamp=start_timestamp,
        end_timestamp=end_timestamp,
        purpose=purpose,
        action=action
    )
    
    # Note: We would need to modify the service to accept userUuid filter
    # For now, this is a placeholder endpoint structure
    return list_logs(filters, page, page_size, db) 