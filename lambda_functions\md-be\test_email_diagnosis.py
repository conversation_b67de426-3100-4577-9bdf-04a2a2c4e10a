#!/usr/bin/env python3
"""
Email Diagnosis Script for Admin Creation Function
Tests email sending functionality and AWS credentials
"""

import sys
import os
import logging
from typing import Dict, Any

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import settings
from utils.email_service import send_admin_welcome_email_with_password_change, verify_ses_configuration
import boto3
from botocore.exceptions import ClientError, NoCredentialsError

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_aws_credentials():
    """Test AWS credentials configuration"""
    print("\n" + "="*60)
    print("TESTING AWS CREDENTIALS CONFIGURATION")
    print("="*60)
    
    # Check environment variables
    print(f"AWS_ACCESS_KEY_ID: {'SET' if settings.AWS_ACCESS_KEY_ID else 'NOT SET'}")
    print(f"AWS_SECRET_ACCESS_KEY: {'SET' if settings.AWS_SECRET_ACCESS_KEY else 'NOT SET'}")
    print(f"AWS_REGION: {settings.AWS_REGION}")
    
    if settings.AWS_ACCESS_KEY_ID:
        print(f"AWS_ACCESS_KEY_ID (first 8 chars): {settings.AWS_ACCESS_KEY_ID[:8]}...")
    
    # Test STS (Security Token Service) to verify credentials
    try:
        # Use default AWS credential chain when possible
        if settings.AWS_ACCESS_KEY_ID and settings.AWS_SECRET_ACCESS_KEY:
            sts_client = boto3.client(
                'sts',
                region_name=settings.AWS_REGION,
                aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
            )
        else:
            sts_client = boto3.client(
                'sts',
                region_name=settings.AWS_REGION
            )
        
        identity = sts_client.get_caller_identity()
        print(f"✅ AWS Credentials Valid")
        print(f"   Account: {identity.get('Account')}")
        print(f"   User ARN: {identity.get('Arn')}")
        return True
        
    except NoCredentialsError:
        print("❌ AWS Credentials Not Found")
        return False
    except ClientError as e:
        print(f"❌ AWS Credentials Invalid: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error testing credentials: {e}")
        return False

def test_ses_configuration():
    """Test SES configuration and permissions"""
    print("\n" + "="*60)
    print("TESTING SES CONFIGURATION")
    print("="*60)
    
    try:
        # Use default AWS credential chain when possible
        if settings.AWS_ACCESS_KEY_ID and settings.AWS_SECRET_ACCESS_KEY:
            ses_client = boto3.client(
                'ses',
                region_name=settings.AWS_REGION,
                aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
            )
        else:
            ses_client = boto3.client(
                'ses',
                region_name=settings.AWS_REGION
            )
        
        # Test SES access by listing identities
        response = ses_client.list_identities()
        print("✅ SES Access Successful")
        
        # Check sender email verification
        verification_response = ses_client.get_identity_verification_attributes(
            Identities=[settings.EMAIL_FROM_ADDRESS]
        )
        
        verification_status = verification_response.get('VerificationAttributes', {}).get(
            settings.EMAIL_FROM_ADDRESS, {}
        ).get('VerificationStatus', 'NotStarted')
        
        print(f"Email From Address: {settings.EMAIL_FROM_ADDRESS}")
        print(f"Verification Status: {verification_status}")
        
        if verification_status == 'Success':
            print("✅ Sender Email Verified")
            return True
        else:
            print(f"❌ Sender Email Not Verified (Status: {verification_status})")
            return False
            
    except ClientError as e:
        error_code = e.response['Error']['Code']
        error_message = e.response['Error']['Message']
        print(f"❌ SES Error: {error_code} - {error_message}")
        return False
    except Exception as e:
        print(f"❌ Unexpected SES error: {e}")
        return False

def test_email_sending(test_email: str):
    """Test actual email sending"""
    print("\n" + "="*60)
    print(f"TESTING EMAIL SENDING TO: {test_email}")
    print("="*60)
    
    # Prepare test admin data
    admin_data = {
        'email': test_email,
        'username': 'test_admin',
        'firstName': 'Test',
        'lastName': 'Admin',
        'roles': ['Administrator'],
        'accessToken': 'test_token_123'
    }
    
    try:
        # Test the email sending function
        email_sent = send_admin_welcome_email_with_password_change(admin_data)
        
        if email_sent:
            print("✅ Email Sent Successfully")
            return True
        else:
            print("❌ Email Sending Failed")
            return False
            
    except Exception as e:
        print(f"❌ Email Sending Error: {e}")
        return False

def test_environment_comparison():
    """Compare local vs staging environment settings"""
    print("\n" + "="*60)
    print("ENVIRONMENT CONFIGURATION COMPARISON")
    print("="*60)
    
    print("Current Environment Settings:")
    print(f"  EMAIL_FROM_ADDRESS: {settings.EMAIL_FROM_ADDRESS}")
    print(f"  EMAIL_FROM_NAME: {settings.EMAIL_FROM_NAME}")
    print(f"  COMPANY_NAME: {settings.COMPANY_NAME}")
    print(f"  APP_BASE_URL: {settings.APP_BASE_URL}")
    print(f"  AWS_REGION: {settings.AWS_REGION}")
    
    # Check if we're in local or staging environment
    if "localhost" in settings.APP_BASE_URL or "127.0.0.1" in settings.APP_BASE_URL:
        print("🏠 Running in LOCAL environment")
    elif "stage" in settings.APP_BASE_URL:
        print("🚀 Running in STAGING environment")
    else:
        print("🌐 Running in UNKNOWN environment")

def main():
    """Main diagnosis function"""
    print("EMAIL DIAGNOSIS SCRIPT FOR ADMIN CREATION")
    print("="*60)
    
    test_email = "<EMAIL>"
    
    # Run all tests
    credentials_ok = test_aws_credentials()
    ses_ok = test_ses_configuration()
    test_environment_comparison()
    
    if credentials_ok and ses_ok:
        email_ok = test_email_sending(test_email)
        
        print("\n" + "="*60)
        print("DIAGNOSIS SUMMARY")
        print("="*60)
        print(f"✅ AWS Credentials: {'PASS' if credentials_ok else 'FAIL'}")
        print(f"✅ SES Configuration: {'PASS' if ses_ok else 'FAIL'}")
        print(f"✅ Email Sending: {'PASS' if email_ok else 'FAIL'}")
        
        if credentials_ok and ses_ok and email_ok:
            print("\n🎉 ALL TESTS PASSED - Email system is working correctly!")
        else:
            print("\n⚠️  SOME TESTS FAILED - Check the issues above")
    else:
        print("\n" + "="*60)
        print("DIAGNOSIS SUMMARY")
        print("="*60)
        print(f"❌ AWS Credentials: {'PASS' if credentials_ok else 'FAIL'}")
        print(f"❌ SES Configuration: {'PASS' if ses_ok else 'FAIL'}")
        print("❌ Email Sending: SKIPPED (Prerequisites failed)")
        
        print("\n⚠️  CRITICAL ISSUES FOUND - Fix credentials/SES configuration first")

if __name__ == "__main__":
    main()
