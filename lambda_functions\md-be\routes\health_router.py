# Health Check Router for database connectivity and AWS Secrets Manager authentication

from fastapi import APIRouter, HTTPException, status
from fastapi.responses import JSONResponse
from db.db import get_connection_manager
from db.secrets_manager_auth import get_secrets_auth
import logging
from datetime import datetime

# Configure logging
logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/health/database", tags=["Health Checks"])
async def check_database_health():
    # Test the current database connection
    try:
        from db.connection_manager import initialize_connection_manager
        
        # Initialize connection manager (this will trigger credential retrieval and logging)
        connection_manager = initialize_connection_manager(
            secret_name="stg_member_db_rw_user",
            region="us-east-1"
        )
        test_result = connection_manager.test_connection()
        
        if test_result["status"] == "success":
            logger.info("Database health check passed")
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content={
                    "status": "healthy",
                    "timestamp": datetime.now().isoformat(),
                    "database_info": test_result,
                    "connection_info": connection_manager.get_connection_info(),
                    "logs": connection_manager.get_logs(),
                    "message": "Database connection is healthy"
                }
            )
        else:
            logger.error(f"Database health check failed: {test_result}")
            return JSONResponse(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                content={
                    "status": "unhealthy",
                    "timestamp": datetime.now().isoformat(),
                    "database_info": test_result,
                    "connection_info": connection_manager.get_connection_info(),
                    "logs": connection_manager.get_logs(),
                    "message": "Database connection failed",
                    "troubleshooting_steps": [
                        "Check Lambda execution role has 'secretsmanager:GetSecretValue' permission",
                        "Verify secret 'stg_member_db_rw_user' exists in us-east-1 region",
                        "Check database server is accessible",
                        "Verify database credentials are correct"
                    ]
                }
            )
            
    except Exception as e:
        import traceback
        error_traceback = traceback.format_exc()
        logger.error(f"Database health check error: {str(e)}")
        logger.error(f"Traceback: {error_traceback}")

        # Get detailed error information from secrets manager
        detailed_error = None
        try:
            from db.db import get_connection_manager
            connection_manager = get_connection_manager()
            if hasattr(connection_manager, 'secrets_auth'):
                detailed_error = connection_manager.secrets_auth.get_detailed_error()
        except Exception as detail_error:
            logger.error(f"Failed to get detailed error: {str(detail_error)}")

        error_content = {
            "status": "error",
            "timestamp": datetime.now().isoformat(),
            "error": str(e),
            "error_type": type(e).__name__,
            "traceback": error_traceback,
            "message": "Health check failed due to internal error",
            "troubleshooting_steps": [
                "Check Lambda execution role has 'secretsmanager:GetSecretValue' permission",
                "Verify secret 'stg_member_db_rw_user' exists in us-east-1 region",
                "Check CloudWatch logs for detailed error messages",
                "Verify AWS credentials are properly configured"
            ]
        }

        # Add detailed error information if available
        if detailed_error:
            error_content["detailed_authentication_error"] = detailed_error

        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content=error_content
        )
