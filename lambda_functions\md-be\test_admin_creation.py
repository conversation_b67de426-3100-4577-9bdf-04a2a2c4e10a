#!/usr/bin/env python3
"""
Test Admin Creation Function End-to-End
Tests the complete create_admin_cognito function locally
"""

import sys
import os
import logging
from unittest.mock import Mock

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from schemas.admin import Admin_cognito_create
from services.admin_api import create_admin_cognito
from jose import jwt
import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_mock_token():
    """Create a mock JWT token for testing"""
    payload = {
        'sub': 'test-admin-id-12345',
        'cognitoid': 'test-admin-id-12345',
        'iat': datetime.datetime.utcnow(),
        'exp': datetime.datetime.utcnow() + datetime.timedelta(hours=1)
    }
    # Create a simple token (not properly signed, but sufficient for testing)
    return jwt.encode(payload, 'test-secret', algorithm='HS256')

def create_mock_db():
    """Create a mock database session"""
    mock_db = Mock()
    mock_db.add = Mock()
    mock_db.commit = Mock()
    mock_db.refresh = Mock()
    mock_db.rollback = Mock()
    return mock_db

def test_admin_creation_local():
    """Test admin creation function locally"""
    print("\n" + "="*60)
    print("TESTING ADMIN CREATION FUNCTION LOCALLY")
    print("="*60)
    
    # Create test data with unique identifiers
    import random
    timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
    random_num = random.randint(1000, 9999)
    test_email = f"testadmin_{timestamp}_{random_num}@imcourageous.com"
    test_username = f"testadmin_{timestamp}_{random_num}"
    
    new_admin = Admin_cognito_create(
        email=test_email,
        username=test_username,
        firstName="Test",
        lastName="Admin",
        phone="+1234567890",
        countryCode="US",
        roles=["Administrator"]
    )
    
    # Create mock token and database
    token = create_mock_token()
    mock_db = create_mock_db()
    
    print(f"Test Email: {test_email}")
    print(f"Test Username: {test_username}")
    print(f"Test Token: {token[:50]}...")
    
    try:
        # Call the actual function
        result = create_admin_cognito(token, new_admin, mock_db)
        
        print("✅ Admin Creation Function Executed Successfully")
        print(f"Response Status: {result.get('status_code', 'Unknown')}")
        print(f"Response Message: {result.get('message', 'No message')}")
        
        # Check if email was sent
        response_data = result.get('data', {})
        email_sent = response_data.get('emailSent', False)
        print(f"Email Sent: {'✅ YES' if email_sent else '❌ NO'}")
        
        if email_sent:
            print(f"Admin UUID: {response_data.get('uuid', 'Not provided')}")
            print(f"Admin Username: {response_data.get('username', 'Not provided')}")
            print(f"Admin Email: {response_data.get('email', 'Not provided')}")
            print(f"Temp Password: {'✅ YES' if response_data.get('isTempPassword') else '❌ NO'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Admin Creation Failed: {e}")
        logger.exception("Full error details:")
        return False

def test_staging_environment():
    """Test the staging environment endpoint"""
    print("\n" + "="*60)
    print("TESTING STAGING ENVIRONMENT")
    print("="*60)
    
    staging_url = "https://stage-api.co-nomi.app/api/admin/register"
    print(f"Staging URL: {staging_url}")
    print("Note: Manual testing required for staging environment")
    print("Reason: Requires valid JWT token and proper authentication")
    
    # Instructions for manual testing
    print("\nManual Testing Instructions:")
    print("1. Get a valid JWT token from staging login")
    print("2. Use Postman or curl to test the endpoint")
    print("3. Compare the response with local testing results")
    print("4. Check staging logs for credential errors")

def compare_environments():
    """Compare local vs staging environment configurations"""
    print("\n" + "="*60)
    print("ENVIRONMENT COMPARISON")
    print("="*60)
    
    from config import settings
    
    print("Current Configuration:")
    print(f"  AWS_ACCESS_KEY_ID: {'SET' if settings.AWS_ACCESS_KEY_ID else 'NOT SET'}")
    print(f"  AWS_SECRET_ACCESS_KEY: {'SET' if settings.AWS_SECRET_ACCESS_KEY else 'NOT SET'}")
    print(f"  EMAIL_FROM_ADDRESS: {settings.EMAIL_FROM_ADDRESS}")
    print(f"  APP_BASE_URL: {settings.APP_BASE_URL}")
    print(f"  COGNITO_USER_POOL_ID: {settings.COGNITO_USER_POOL_ID}")
    print(f"  COGNITO_CLIENT_ID: {settings.COGNITO_CLIENT_ID}")
    
    print("\nPotential Issues in Staging:")
    print("1. AWS credentials might not be set in staging environment variables")
    print("2. IAM permissions might be different between local and staging")
    print("3. SES configuration might be different")
    print("4. Environment variables might not be loaded correctly")

def main():
    """Main test function"""
    print("ADMIN CREATION FUNCTION TESTING")
    print("="*60)
    
    # Test locally first
    local_success = test_admin_creation_local()
    
    # Compare environments
    compare_environments()
    
    # Staging test instructions
    test_staging_environment()
    
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    print(f"Local Testing: {'✅ PASS' if local_success else '❌ FAIL'}")
    print("Staging Testing: ⏳ MANUAL REQUIRED")
    
    if local_success:
        print("\n🎉 Local admin creation is working correctly!")
        print("📧 Email sending is functional locally")
        print("🔍 Issue is likely in staging environment configuration")
        
        print("\nNext Steps:")
        print("1. Check staging environment variables")
        print("2. Verify AWS credentials in staging")
        print("3. Check staging IAM permissions")
        print("4. Compare staging vs local AWS configuration")
    else:
        print("\n⚠️  Local admin creation failed")
        print("🔧 Fix local issues first before testing staging")

if __name__ == "__main__":
    main()
