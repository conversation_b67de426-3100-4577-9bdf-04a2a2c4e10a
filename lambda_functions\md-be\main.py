from fastapi import Depends, <PERSON>AP<PERSON>
from controller.member_controller import <PERSON><PERSON><PERSON><PERSON><PERSON>
from routes import member_authentication_router, member_router, health_router
from fastapi.middleware.cors import CORSMiddleware
from routes import admins_router, rbac_router, organization_router, log_router
from slowapi import Limiter
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded
from fastapi.responses import JSONResponse
from slowapi.middleware import SlowAPIMiddleware
from mangum import Mangum
import uvicorn
import logging

# Import all models to ensure they are registered with SQLAlchemy
from models import *

logger = logging.getLogger(__name__)


app = FastAPI()
memberController = MemberController()

limiter = Limiter(key_func=get_remote_address, default_limits=["40/minute"])
app.state.limiter = limiter

@app.exception_handler(RateLimitExceeded)
def rate_limit_handler(request, exc):
    return JSONResponse(
        status_code=429,
        content={
            "detail": {
                "message": "Rate limit exceeded",
                "status_code": 429
            }
        },
    )

# Enable CORS for all origins
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(SlowAPIMiddleware)

@app.get("/", tags=["root"])
async def root():
    return {"message": "Welcome to the US Chamber of Commerce API"}


app.include_router(health_router.router, prefix="", tags=["Health Checks"])
app.include_router(member_authentication_router.router, prefix="/api/members", tags=["Member Authentication"])
app.include_router(member_router.router, prefix="/api/members", tags=["Members"])
app.include_router(admins_router.router, prefix="/api/admin", tags=["Admin"])
app.include_router(rbac_router.router, prefix="/api/rbac", tags=["RBAC"])
app.include_router(organization_router.router, prefix="/api/organizations", tags=["Organizations"])
app.include_router(log_router.router, prefix="/api", tags=["Logs"])

# to allow AWS Lambda to run the app
def handler(event, context):
    mangum_handler = Mangum(app)
    return mangum_handler(event, context)

if __name__ == "__main__":
    uvicorn.run(
        "main:app"
    )
