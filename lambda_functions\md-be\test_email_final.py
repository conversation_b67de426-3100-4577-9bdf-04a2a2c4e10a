#!/usr/bin/env python3
"""
Final Email Test for Admin Creation
Tests email sending to the requested email address
"""

import sys
import os
import logging

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.email_service import send_admin_welcome_email_with_password_change

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_email_to_requested_address():
    """Test email sending to the specific requested email address"""
    print("\n" + "="*60)
    print("TESTING EMAIL TO REQUESTED ADDRESS")
    print("="*60)
    
    # Use the exact email address requested by the user
    test_email = "<EMAIL>"
    
    # Prepare test admin data
    admin_data = {
        'email': test_email,
        'username': 'test_admin_final',
        'firstName': 'Test',
        'lastName': 'Admin',
        'roles': ['Administrator'],
        'accessToken': 'test_token_for_password_change_123456'
    }
    
    print(f"Sending welcome email to: {test_email}")
    print(f"Admin Username: {admin_data['username']}")
    print(f"Admin Name: {admin_data['firstName']} {admin_data['lastName']}")
    print(f"Roles: {admin_data['roles']}")
    
    try:
        # Test the email sending function
        email_sent = send_admin_welcome_email_with_password_change(admin_data)
        
        if email_sent:
            print("✅ Email Sent Successfully!")
            print("📧 Check the email inbox for the welcome message")
            print("🔗 The email should contain a password change link")
            return True
        else:
            print("❌ Email Sending Failed")
            print("Check the logs above for error details")
            return False
            
    except Exception as e:
        print(f"❌ Email Sending Error: {e}")
        logger.exception("Full error details:")
        return False

def main():
    """Main test function"""
    print("FINAL EMAIL TEST FOR ADMIN CREATION")
    print("="*60)
    
    # Test email sending
    success = test_email_to_requested_address()
    
    print("\n" + "="*60)
    print("FINAL TEST SUMMARY")
    print("="*60)
    
    if success:
        print("✅ EMAIL SENDING WORKS CORRECTLY!")
        print("📧 Welcome email <NAME_EMAIL>")
        print("🔧 The admin creation function is working locally")
        print("")
        print("STAGING ISSUE DIAGNOSIS:")
        print("The error 'Unable to locate credentials' in staging indicates:")
        print("1. AWS credentials are not properly configured in staging environment")
        print("2. Environment variables AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY are missing")
        print("3. Or the staging deployment doesn't have access to the credentials")
        print("")
        print("SOLUTION:")
        print("1. Check staging environment variables configuration")
        print("2. Ensure AWS credentials are set in staging deployment")
        print("3. Verify IAM permissions for SES in staging environment")
        print("4. Check if staging uses different credential management (IAM roles, etc.)")
        
    else:
        print("❌ EMAIL SENDING FAILED")
        print("🔧 Fix the email configuration before testing staging")

if __name__ == "__main__":
    main()
