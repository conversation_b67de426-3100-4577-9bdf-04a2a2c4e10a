# AWS Secrets Manager Authentication Module

import boto3
import json
import logging
from typing import Dict, Any
from botocore.exceptions import ClientError, NoCredentialsError, BotoCoreError

# Configure logging
logger = logging.getLogger(__name__)

class SecretsManagerAuth:
    # Manages AWS Secrets Manager authentication for database connections

    def __init__(self, secret_name: str, region: str):
        self.secret_name = secret_name
        self.region = region

        # Secrets Manager client
        self._secrets_client = None
        self._client_error = None

        logger.info(f"Secrets Manager Auth initialized for secret '{secret_name}' in region '{region}'")



    def _get_secrets_client(self):
        # Store detailed error information for API responses
        self._detailed_error = None

        # Use IAM role credentials (default AWS credential chain)
        if self._secrets_client is None:
            try:
                self._secrets_client = boto3.client(
                    'secretsmanager',
                    region_name=self.region
                )
                self._client_logs = "✅ AWS credential authentication successful"
                return self._secrets_client

            except Exception as iam_error:
                self._detailed_error = {
                    "step": "iam_authentication",
                    "error": str(iam_error),
                    "error_type": type(iam_error).__name__,
                    "region": self.region,
                    "message": "IAM role authentication failed"
                }
                self._secrets_client = None
                self._client_error = f"IAM role authentication failed: {iam_error}"
                self._client_logs = f"❌ Failed to create Secrets Manager client: {str(iam_error)}"
        return self._secrets_client

    def get_detailed_error(self) -> Dict[str, Any]:
        """Get detailed error information for API responses"""
        if hasattr(self, '_detailed_error') and self._detailed_error:
            return self._detailed_error
        elif hasattr(self, '_credential_error') and self._credential_error:
            return self._credential_error
        elif hasattr(self, '_client_error') and self._client_error:
            return {
                "step": "client_creation",
                "error": self._client_error,
                "region": self.region,
                "secret_name": self.secret_name,
                "message": "Client creation failed"
            }
        else:
            return {
                "step": "no_error",
                "message": "No errors detected",
                "region": self.region,
                "secret_name": self.secret_name
            }
    
    def get_database_credentials(self) -> Dict[str, str]:
        # Retrieve database credentials and configuration from AWS Secrets Manager
        try:
            client = self._get_secrets_client()

            # Check if client creation failed
            if client is None:
                if hasattr(self, '_client_error') and self._client_error:
                    raise Exception(self._client_error)
                else:
                    raise Exception("Secrets Manager client is not available")

            logger.debug(f"Retrieving database configuration from secret '{self.secret_name}'")

            # Get the secret value
            response = client.get_secret_value(SecretId=self.secret_name)

            # Parse the secret string (should be JSON)
            secret_string = response['SecretString']
            credentials = json.loads(secret_string)

            # Log the credentials for debugging (be careful with sensitive data in production)
            credential_logs = [
                f"🔐 Retrieved credentials from secret '{self.secret_name}':",
                f"   Username: {credentials.get('username', 'NOT_FOUND')}",
                f"   Host: {credentials.get('host', 'NOT_FOUND')}",
                f"   Port: {credentials.get('port', 'NOT_FOUND')}",
                f"   Database: {credentials.get('dbname', 'NOT_FOUND')}",
                f"   Password: {'*' * len(credentials.get('password', '')) if credentials.get('password') else 'NOT_FOUND'}"
            ]
            
            for log in credential_logs:
                logger.info(log)
            
            # Store logs for response
            self._credential_logs = credential_logs

            # Validate required fields
            required_fields = ['username', 'password', 'host', 'port', 'dbname']
            for field in required_fields:
                if field not in credentials:
                    raise Exception(f"Secret does not contain '{field}' field")

            logger.info("Database configuration retrieved successfully from Secrets Manager")

            
            return {
                'username': credentials['username'],
                'password': credentials['password'],
                'host': credentials['host'],
                'port': credentials['port'],
                'dbname': credentials['dbname']
            }
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse secret as JSON: {str(e)}")
            return self._get_empty_credentials(f"JSON parsing error: {str(e)}")
        except NoCredentialsError as e:
            logger.error("AWS credentials not found or invalid")
            return self._get_empty_credentials(f"AWS credentials not found: {str(e)}")
        except ClientError as e:
            error_code = e.response['Error']['Code']
            error_message = e.response['Error']['Message']
            logger.error(f"AWS ClientError ({error_code}): {error_message}")
            return self._get_empty_credentials(f"AWS ClientError ({error_code}): {error_message}")
        except BotoCoreError as e:
            logger.error(f"AWS BotoCoreError: {str(e)}")
            return self._get_empty_credentials(f"AWS BotoCoreError: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error retrieving database credentials: {str(e)}")
            return self._get_empty_credentials(f"Unexpected error: {str(e)}")

    def _get_empty_credentials(self, error_info: str = "Unknown error") -> Dict[str, str]:
        # Store credential error for detailed reporting
        self._credential_error = {
            "step": "credential_retrieval",
            "error": error_info,
            "secret_name": self.secret_name,
            "region": self.region,
            "message": "Failed to retrieve valid credentials from secret"
        }
        # Return empty credentials to prevent 503 errors
        return {
            'username': '',
            'password': '',
            'host': '',
            'port': '',
            'dbname': ''
        }
    
    def get_password(self) -> str:
        # Get just the password from AWS Secrets Manager
        credentials = self.get_database_credentials()
        return credentials['password']

    def get_username(self) -> str:
        # Get just the username from AWS Secrets Manager
        credentials = self.get_database_credentials()
        return credentials['username']

    def get_host(self) -> str:
        # Get just the host from AWS Secrets Manager
        credentials = self.get_database_credentials()
        return credentials['host']

    def get_port(self) -> int:
        # Get just the port from AWS Secrets Manager
        credentials = self.get_database_credentials()
        return int(credentials['port'])

    def get_dbname(self) -> str:
        # Get just the database name from AWS Secrets Manager
        credentials = self.get_database_credentials()
        return credentials['dbname']
    
    def test_secret_access(self) -> Dict[str, Any]:
        # Test access to the secret without retrieving sensitive data
        try:
            # Try to retrieve credentials
            credentials = self.get_database_credentials()
            
            # Return success without exposing sensitive data
            return {
                "status": "success",
                "secret_name": self.secret_name,
                "region": self.region,
                "has_username": bool(credentials.get('username')),
                "has_password": bool(credentials.get('password')),
                "has_host": bool(credentials.get('host')),
                "has_port": bool(credentials.get('port')),
                "has_dbname": bool(credentials.get('dbname')),
                "message": "Secret access test successful",
                "logs": self.get_logs()
            }
            
        except Exception as e:
            logger.error(f"Secret access test failed: {str(e)}")
            return {
                "status": "error",
                "secret_name": self.secret_name,
                "region": self.region,
                "error_message": str(e),
                "message": "Secret access test failed",
                "logs": self.get_logs()
            }
    
    def get_logs(self) -> Dict[str, Any]:
        # Get all logs for response
        logs = {
            "client_creation": getattr(self, '_client_logs', 'No client creation logs'),
            "credential_retrieval": getattr(self, '_credential_logs', ['No credential logs']),
            "client_error": getattr(self, '_client_error', None)
        }
        return logs

    def get_secret_info(self) -> Dict[str, Any]:
        # Get information about the secret configuration
        # Ensure client is created first
        client = self._get_secrets_client()
        return {
            "secret_name": self.secret_name,
            "region": self.region,
            "client_created": client is not None
        }


# Global secrets manager auth instance
_secrets_auth = None

def get_secrets_auth() -> SecretsManagerAuth:
    # Get the global Secrets Manager auth instance
    global _secrets_auth
    if _secrets_auth is None:
        raise RuntimeError("Secrets Manager Auth not initialized. Call initialize_secrets_auth() first.")
    return _secrets_auth

def initialize_secrets_auth(secret_name: str, region: str) -> SecretsManagerAuth:
    # Initialize the global Secrets Manager auth
    global _secrets_auth
    _secrets_auth = SecretsManagerAuth(secret_name, region)
    logger.info("Global Secrets Manager Auth initialized")
    return _secrets_auth
