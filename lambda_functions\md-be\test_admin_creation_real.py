#!/usr/bin/env python3
"""
Test Admin Creation Function with Real Database
Tests the complete create_admin_cognito function with actual database
"""

import sys
import os
import logging
import datetime
import random

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from schemas.admin import Admin_cognito_create
from services.admin_api import create_admin_cognito
from db.db import get_db
from jose import jwt

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_mock_token():
    """Create a mock JWT token for testing"""
    payload = {
        'sub': 'test-admin-id-12345',
        'cognitoid': 'test-admin-id-12345',
        'iat': datetime.datetime.now(datetime.timezone.utc),
        'exp': datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(hours=1)
    }
    # Create a simple token (not properly signed, but sufficient for testing)
    return jwt.encode(payload, 'test-secret', algorithm='HS256')

def test_admin_creation_with_real_db():
    """Test admin creation function with real database"""
    print("\n" + "="*60)
    print("TESTING ADMIN CREATION WITH REAL DATABASE")
    print("="*60)
    
    # Create test data with unique identifiers
    timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
    random_num = random.randint(1000, 9999)
    test_email = "<EMAIL>"  # Use the requested email
    test_username = f"testadmin_{timestamp}_{random_num}"
    
    new_admin = Admin_cognito_create(
        email=test_email,
        username=test_username,
        firstName="Test",
        lastName="Admin",
        phone="+1234567890",
        countryCode="US",
        roles=["Administrator"]
    )
    
    # Create mock token
    token = create_mock_token()
    
    print(f"Test Email: {test_email}")
    print(f"Test Username: {test_username}")
    print(f"Test Token: {token[:50]}...")
    
    # Get real database session
    db_generator = get_db()
    db = next(db_generator)
    
    try:
        # Call the actual function with real database
        result = create_admin_cognito(token, new_admin, db)
        
        print("✅ Admin Creation Function Executed Successfully")
        print(f"Response Status: {result.get('status_code', 'Unknown')}")
        print(f"Response Message: {result.get('message', 'No message')}")
        
        # Check if email was sent
        response_data = result.get('data', {})
        email_sent = response_data.get('emailSent', False)
        print(f"Email Sent: {'✅ YES' if email_sent else '❌ NO'}")
        
        if email_sent:
            print(f"Admin UUID: {response_data.get('uuid', 'Not provided')}")
            print(f"Admin Username: {response_data.get('username', 'Not provided')}")
            print(f"Admin Email: {response_data.get('email', 'Not provided')}")
            print(f"Temp Password: {'✅ YES' if response_data.get('isTempPassword') else '❌ NO'}")
        
        return True, result
        
    except Exception as e:
        print(f"❌ Admin Creation Failed: {e}")
        logger.exception("Full error details:")
        return False, None
        
    finally:
        # Close database session
        try:
            db.close()
        except:
            pass

def cleanup_test_admin(username):
    """Clean up test admin from Cognito (optional)"""
    print(f"\nNote: Test admin '{username}' was created in Cognito.")
    print("You may want to manually delete it from AWS Cognito console if needed.")

def main():
    """Main test function"""
    print("ADMIN CREATION FUNCTION TESTING WITH REAL DATABASE")
    print("="*60)
    
    # Test with real database
    success, result = test_admin_creation_with_real_db()
    
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    print(f"Real Database Testing: {'✅ PASS' if success else '❌ FAIL'}")
    
    if success:
        print("\n🎉 Admin creation is working correctly!")
        print("📧 Email sending is functional")
        print("💾 Database operations are successful")
        
        # Check email sent status
        if result and result.get('data', {}).get('emailSent'):
            print("✅ Email was sent successfully")
        else:
            print("⚠️  Email sending status unclear - check logs above")
            
        print("\nThe issue with staging is likely:")
        print("1. Missing AWS credentials in staging environment")
        print("2. Different IAM permissions in staging")
        print("3. Environment variables not loaded correctly")
        
    else:
        print("\n⚠️  Admin creation failed")
        print("🔧 Check the error details above")

if __name__ == "__main__":
    main()
